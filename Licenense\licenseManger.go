package licenense

import (
	"auth/sysinfo"
	"crypto/ed25519"
	"crypto/rand"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"log"
	"net"
	"os"
	"runtime"
	"strings"
	"time"

	"github.com/beevik/ntp"
)

// OSType 操作系统类型
type OSType string

const (
	OSWindows OSType = "windows"
	OSLinux   OSType = "linux"
	OSDarwin  OSType = "darwin" // macOS
	OSFreeBSD OSType = "freebsd"
	OSAll     OSType = "all" // 支持所有操作系统
)

// HostInfo 主机信息
type HostInfo struct {
	Fingerprint  string    `json:"fingerprint"`   // 主机指纹
	OSType       OSType    `json:"os_type"`       // 操作系统类型
	OSVersion    string    `json:"os_version"`    // 操作系统版本
	Hostname     string    `json:"hostname"`      // 主机名
	RegisterTime time.Time `json:"register_time"` // 注册时间
}

type Feature struct {
	Name    string `json:"name"`
	Desc    string `json:"desc"`
	Allowed bool   `json:"allowed"`
}
type Features struct {
	ClientFeatures []Feature `json:"client_features"`
	ServerFeatures []Feature `json:"server_features"`
}
// LicenseInfo 授权信息结构
type LicenseInfo struct {
	UserName        string     `json:"user_name"`
	Company         string     `json:"company"`
	ProductName     string     `json:"product_name"`
	Version         string     `json:"version"`
	ExpiryDate      time.Time  `json:"expiry_date"`
	Features        Features   `json:"features"`
	MaxHosts        int        `json:"max_hosts"`
	RegisteredHosts []HostInfo `json:"registered_hosts"` // 已注册的主机信息
	IsTrial         bool       `json:"is_trial"`
	TrialStartDate  time.Time  `json:"trial_start_date"`
	TrialDuration   int        `json:"trial_duration"`
	SupportedOS     []OSType   `json:"supported_os"` // 支持的操作系统类型
}

// SignedLicense 签名后的授权信息
type SignedLicense struct {
	License   LicenseInfo `json:"license"`
	Signature []byte      `json:"signature"`
}


// LicenseConfig 授权配置文件结构
type LicenseConfig struct {
	UserName          string   `json:"user_name"`
	Company           string   `json:"company"`
	ProductName       string   `json:"product_name"`
	DeveloperName     string   `json:"developer_name"`
	Version           string   `json:"version"`
	IsTrial           bool     `json:"is_trial"`
	ValidYears        int      `json:"valid_years"`
	TrialDurationDays int      `json:"trial_duration_days"`
	MaxHosts          int64    `json:"max_hosts"`
	Feature           Features `json:"features"`
	SupportedOS       []string `json:"supported_os"`
	OutputFile        string   `json:"output_file"`
	RegisterHost      bool     `json:"register_host"`
}

// LicenseManager 授权管理器
type LicenseManager struct {
	publicKey  ed25519.PublicKey
	privateKey ed25519.PrivateKey
}

// NewLicenseManager 创建新的授权管理器
func NewLicenseManager(keyFile string) *LicenseManager {
	// 尝试从文件加载密钥
	pub, priv, err := loadKeys(keyFile)
	if err != nil {
		// 如果加载失败，生成新密钥并保存
		fmt.Println("未找到密钥文件，生成新的密钥对...")
		pub, priv, err = ed25519.GenerateKey(rand.Reader)
		if err != nil {
			log.Fatal("生成密钥失败:", err)
		}

		// 保存密钥到文件
		err = saveKeys(pub, priv, keyFile)
		if err != nil {
			log.Printf("保存密钥失败: %v", err)
		} else {
			fmt.Println("密钥已保存到 keys.json")
		}
	}

	return &LicenseManager{
		publicKey:  pub,
		privateKey: priv,
	}
}

// saveKeys 保存密钥到文件
func saveKeys(pub ed25519.PublicKey, priv ed25519.PrivateKey, keyFile string) error {
	keys := map[string][]byte{
		"public_key":  pub,
		"private_key": priv,
	}

	data, err := json.MarshalIndent(keys, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化密钥失败: %v", err)
	}

	return os.WriteFile(keyFile, data, 0600) // 只有所有者可读写
}

// loadKeys 从文件加载密钥
func loadKeys(keyFile string) (ed25519.PublicKey, ed25519.PrivateKey, error) {
	data, err := os.ReadFile(keyFile)
	if err != nil {
		return nil, nil, err
	}

	var keys map[string][]byte
	err = json.Unmarshal(data, &keys)
	if err != nil {
		return nil, nil, fmt.Errorf("解析密钥文件失败: %v", err)
	}

	pub, ok := keys["public_key"]
	if !ok {
		return nil, nil, fmt.Errorf("密钥文件中缺少公钥")
	}

	priv, ok := keys["private_key"]
	if !ok {
		return nil, nil, fmt.Errorf("密钥文件中缺少私钥")
	}

	return ed25519.PublicKey(pub), ed25519.PrivateKey(priv), nil
}

// GetCurrentHostInfo 获取当前主机信息
func GetCurrentHostInfo() (*HostInfo, error) {
	// 获取主机名
	hostname, err := os.Hostname()
	if err != nil {
		return nil, err
	}

	// 获取MAC地址
	interfaces, err := net.Interfaces()
	if err != nil {
		return nil, err
	}

	var macAddresses []string
	for _, iface := range interfaces {
		if iface.Flags&net.FlagUp != 0 && iface.Flags&net.FlagLoopback == 0 {
			if iface.HardwareAddr != nil {
				macAddresses = append(macAddresses, iface.HardwareAddr.String())
			}
		}
	}

	// 获取操作系统信息
	osType := OSType(runtime.GOOS)
	osVersion := runtime.GOARCH // 简化版本信息，实际可以获取更详细的版本

	//hfi := sysinfo.AllInfoEx()

	//
	// 组合主机信息生成指纹
	hostInfo := fmt.Sprintf("%s-%s-%s-%s", hostname, osType, osVersion, strings.Join(macAddresses, ","))
	hash := sha256.Sum256([]byte(hostInfo))
	fingerprint := hex.EncodeToString(hash[:])

	return &HostInfo{
		Fingerprint:  fingerprint,
		OSType:       osType,
		OSVersion:    osVersion,
		Hostname:     hostname,
		RegisterTime: time.Now(),
	}, nil
}

// GetHostFingerprint 获取主机指纹（保持向后兼容）
func GetHostFingerprint() (string, error) {
	hostInfo, err := GetCurrentHostInfo()
	if err != nil {
		return "", err
	}
	return hostInfo.Fingerprint, nil
}

// CreateTrialLicense 创建试用授权
func CreateTrialLicense(userName, company, productName, version string, maxHosts int, features []string, supportedOS []OSType, trialDurationDays int) LicenseInfo {
	now := time.Now()
	return LicenseInfo{
		UserName:        userName,
		Company:         company,
		ProductName:     productName,
		Version:         version,
		ExpiryDate:      now.AddDate(0, 0, trialDurationDays),
		Features:        features,
		MaxHosts:        maxHosts,
		RegisteredHosts: []HostInfo{},
		IsTrial:         true,
		TrialStartDate:  now,
		TrialDuration:   trialDurationDays,
		SupportedOS:     supportedOS,
	}
}

// CreateFullLicense 创建正式授权
func CreateFullLicense(userName, company, productName, version string, maxHosts int, features []string, validYears int, supportedOS []OSType) LicenseInfo {
	now := time.Now()
	return LicenseInfo{
		UserName:        userName,
		Company:         company,
		ProductName:     productName,
		Version:         version,
		ExpiryDate:      now.AddDate(validYears, 0, 0),
		Features:        features,
		MaxHosts:        maxHosts,
		RegisteredHosts: []HostInfo{},
		IsTrial:         false,
		TrialStartDate:  time.Time{},
		TrialDuration:   0,
		SupportedOS:     supportedOS,
	}
}

// CheckOSSupport 检查当前操作系统是否被支持
func (lm *LicenseManager) CheckOSSupport(signedLicense *SignedLicense, currentOS OSType) error {
	// 如果支持所有操作系统
	for _, supportedOS := range signedLicense.License.SupportedOS {
		if supportedOS == OSAll || supportedOS == currentOS {
			return nil
		}
	}

	return fmt.Errorf("当前操作系统 %s 不在授权支持范围内，支持的系统: %v", currentOS, signedLicense.License.SupportedOS)
}

// VerifyLicense 验证授权信息
func (lm *LicenseManager) VerifyLicense(signedLicense *SignedLicense) error {
	// 序列化授权信息
	licenseData, err := json.Marshal(signedLicense.License)
	if err != nil {
		return fmt.Errorf("序列化授权信息失败: %v", err)
	}

	// 验证签名
	if !ed25519.Verify(lm.publicKey, licenseData, signedLicense.Signature) {
		return fmt.Errorf("授权信息签名验证失败")
	}

	// 检查是否过期
	if time.Now().After(signedLicense.License.ExpiryDate) {
		if signedLicense.License.IsTrial {
			return fmt.Errorf("试用期已过期")
		}
		return fmt.Errorf("授权已过期")
	}

	// 如果是试用版，额外检查试用期限制
	if signedLicense.License.IsTrial {
		trialEndDate := signedLicense.License.TrialStartDate.AddDate(0, 0, signedLicense.License.TrialDuration)
		if time.Now().After(trialEndDate) {
			return fmt.Errorf("试用期已结束")
		}
	}

	return nil
}

// CheckHostLicense 检查当前主机是否有授权
func (lm *LicenseManager) CheckHostLicense(signedLicense *SignedLicense) error {
	// 先验证基本授权信息
	if err := lm.VerifyLicense(signedLicense); err != nil {
		return err
	}

	// 获取当前主机信息
	currentHostInfo, err := GetCurrentHostInfo()
	if err != nil {
		return fmt.Errorf("获取主机信息失败: %v", err)
	}

	// 检查操作系统支持
	if err := lm.CheckOSSupport(signedLicense, currentHostInfo.OSType); err != nil {
		return err
	}

	// 检查当前主机是否在已注册列表中
	for _, registeredHost := range signedLicense.License.RegisteredHosts {
		if registeredHost.Fingerprint == currentHostInfo.Fingerprint {
			return nil // 找到匹配的主机
		}
	}

	return fmt.Errorf("当前主机未授权")
}

// RegisterHost 注册新主机
func (lm *LicenseManager) RegisterHost(signedLicense *SignedLicense, hostInfo *HostInfo) (*SignedLicense, error) {
	// 检查操作系统支持
	if err := lm.CheckOSSupport(signedLicense, hostInfo.OSType); err != nil {
		return nil, err
	}

	// 检查是否超过最大主机数量
	if len(signedLicense.License.RegisteredHosts) >= signedLicense.License.MaxHosts {
		return nil, fmt.Errorf("已达到最大主机数量限制 (%d)", signedLicense.License.MaxHosts)
	}

	// 检查主机是否已注册
	for _, registeredHost := range signedLicense.License.RegisteredHosts {
		if registeredHost.Fingerprint == hostInfo.Fingerprint {
			return signedLicense, nil // 主机已注册
		}
	}

	// 添加新主机
	newLicense := signedLicense.License
	hostInfo.RegisterTime = time.Now()
	newLicense.RegisteredHosts = append(newLicense.RegisteredHosts, *hostInfo)

	// 重新签名
	return lm.GenerateLicense(newLicense)
}

// GenerateLicense 生成签名授权
func (lm *LicenseManager) GenerateLicense(license LicenseInfo) (*SignedLicense, error) {
	// 序列化授权信息
	licenseData, err := json.Marshal(license)
	if err != nil {
		return nil, fmt.Errorf("序列化授权信息失败: %v", err)
	}

	// 使用私钥签名
	if len(lm.privateKey) < 8 {
		return nil, fmt.Errorf("密钥不存在")
	}
	signature := ed25519.Sign(lm.privateKey, licenseData)

	return &SignedLicense{
		License:   license,
		Signature: signature,
	}, nil
}

// SaveLicense 保存授权到文件
func (lm *LicenseManager) SaveLicense(signedLicense *SignedLicense, filename string) error {
	data, err := json.MarshalIndent(signedLicense, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化授权文件失败: %v", err)
	}

	err = os.WriteFile(filename, data, 0644)
	if err != nil {
		return fmt.Errorf("保存授权文件失败: %v", err)
	}

	fmt.Printf("授权文件已保存: %s\n", filename)
	return nil
}

func GetTimeFromNet() time.Time {
	var servers = []string{
		"pool.ntp.org",
		"time.apple.com:123",
		"ntp.aliyun.com:123",
		"time.windows.com:123",
		"ntp.ntsc.ac.cn:123", // 中国国家授时中心
	}
	var find bool = false
	var timeFromNet time.Time
	for _, server := range servers {
		response, err := ntp.Query(server)
		if err != nil {
			fmt.Println("NTP请求失败:", err)
			continue
		}

		// 计算网络延迟后的精确时间
		exactTime := time.Now().Add(response.ClockOffset)
		timeFromNet = exactTime
		fmt.Println("精确网络时间(UTC):", exactTime.UTC().Format(time.RFC3339Nano))
		fmt.Println("本地时间:", exactTime.Local().Format("2006-01-02 15:04:05.999999999 -07:00"))
		find = true
		break

	}
	if !find {
		timeFromNet = time.Now().AddDate(0, 0, 1)
	}
	return timeFromNet

}

// LoadLicense 从文件加载授权
func (lm *LicenseManager) LoadLicense(filename string) (*SignedLicense, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("读取授权文件失败: %v", err)
	}

	var signedLicense SignedLicense
	err = json.Unmarshal(data, &signedLicense)
	if err != nil {
		return nil, fmt.Errorf("解析授权文件失败: %v", err)
	}

	return &signedLicense, nil
}

// GetLicenseStatus 获取授权状态信息
func (lm *LicenseManager) GetLicenseStatus(signedLicense *SignedLicense) map[string]interface{} {
	status := make(map[string]interface{})

	license := signedLicense.License
	now := time.Now()

	status["user_name"] = license.UserName
	status["company"] = license.Company
	status["product"] = fmt.Sprintf("%s %s", license.ProductName, license.Version)
	status["is_trial"] = license.IsTrial
	status["features"] = license.Features
	status["max_hosts"] = license.MaxHosts
	status["registered_hosts_count"] = len(license.RegisteredHosts)
	status["supported_os"] = license.SupportedOS

	// 注册主机详情
	var hostDetails []map[string]interface{}
	for _, host := range license.RegisteredHosts {
		hostDetail := map[string]interface{}{
			"hostname":      host.Hostname,
			"os_type":       host.OSType,
			"os_version":    host.OSVersion,
			"register_time": host.RegisterTime.Format("2006-01-02 15:04:05"),
			"fingerprint":   host.Fingerprint[:16] + "...",
		}
		hostDetails = append(hostDetails, hostDetail)
	}
	status["registered_hosts"] = hostDetails

	if license.IsTrial {
		status["license_type"] = "试用版"
		trialEndDate := license.TrialStartDate.AddDate(0, 0, license.TrialDuration)
		remainingDays := int(time.Until(trialEndDate).Hours() / 24)
		if remainingDays < 0 {
			remainingDays = 0
		}
		status["trial_remaining_days"] = remainingDays
		status["trial_end_date"] = trialEndDate.Format("2006-01-02")
		status["trial_expired"] = now.After(trialEndDate)
	} else {
		status["license_type"] = "正式版"
		remainingDays := int(time.Until(license.ExpiryDate).Hours() / 24)
		if remainingDays < 0 {
			remainingDays = 0
		}
		status["remaining_days"] = remainingDays
		status["expiry_date"] = license.ExpiryDate.Format("2006-01-02")
		status["expired"] = now.After(license.ExpiryDate)
	}

	return status
}

// parseOSTypes 解析操作系统类型列表
func parseOSTypes(osStr string) []OSType {
	if osStr == "" || osStr == "all" {
		return []OSType{OSAll}
	}

	parts := strings.Split(osStr, ",")
	var osTypes []OSType
	for _, part := range parts {
		part = strings.TrimSpace(part)
		switch part {
		case "windows":
			osTypes = append(osTypes, OSWindows)
		case "linux":
			osTypes = append(osTypes, OSLinux)
		case "darwin", "macos":
			osTypes = append(osTypes, OSDarwin)
		case "freebsd":
			osTypes = append(osTypes, OSFreeBSD)
		case "all":
			return []OSType{OSAll}
		default:
			fmt.Printf("警告: 未知的操作系统类型 '%s'，已忽略\n", part)
		}
	}
	if len(osTypes) == 0 {
		return []OSType{OSAll}
	}
	return osTypes
}

// parseFeatures 解析功能列表
func parseFeatures(featuresStr string) []string {
	if featuresStr == "" {
		return []string{"基础功能"}
	}

	parts := strings.Split(featuresStr, ",")
	var features []string
	for _, part := range parts {
		part = strings.TrimSpace(part)
		if part != "" {
			features = append(features, part)
		}
	}
	if len(features) == 0 {
		return []string{"基础功能"}
	}
	return features
}

// LoadLicenseConfig 从JSON文件加载授权配置
func LoadLicenseConfig(filename string) (*LicenseConfig, error) {
	data, err := os.ReadFile(filename)
	if err != nil {
		return nil, fmt.Errorf("读取配置文件失败: %v", err)
	}

	var config LicenseConfig
	err = json.Unmarshal(data, &config)
	if err != nil {
		return nil, fmt.Errorf("解析配置文件失败: %v", err)
	}

	// 设置默认值
	if config.UserName == "" {
		config.UserName = "测试用户"
	}
	if config.Company == "" {
		config.Company = "测试公司"
	}
	if config.ProductName == "" {
		config.ProductName = "测试产品"
	}
	if config.DeveloperName == "" {
		config.DeveloperName = "开发者"
	}
	if config.Version == "" {
		config.Version = "v1.0.0"
	}
	if config.ValidYears == 0 && !config.IsTrial {
		config.ValidYears = 1
	}
	if config.TrialDurationDays == 0 && config.IsTrial {
		config.TrialDurationDays = 30
	}
	if config.MaxHosts == 0 {
		config.MaxHosts = 1
	}
	// 处理 Features 字段
	if len(config.Feature.ClientFeatures) == 0 && len(config.Feature.ServerFeatures) == 0 {
		config.Feature.ClientFeatures = []Feature{
			{Name: "基础功能", Desc: "基本的客户端功能", Allowed: true},
		}
		config.Feature.ServerFeatures = []Feature{
			{Name: "基础服务", Desc: "基本的服务端功能", Allowed: true},
		}
	}
	if len(config.SupportedOS) == 0 {
		config.SupportedOS = []string{"all"}
	}
	if config.OutputFile == "" {
		config.OutputFile = "license.json"
	}

	return &config, nil
}

// ValidateLicenseConfig 验证授权配置的有效性
func ValidateLicenseConfig(config *LicenseConfig) error {
	if config.UserName == "" {
		return fmt.Errorf("用户名不能为空")
	}
	if config.Company == "" {
		return fmt.Errorf("公司名不能为空")
	}
	if config.ProductName == "" {
		return fmt.Errorf("产品名不能为空")
	}
	if config.Version == "" {
		return fmt.Errorf("版本号不能为空")
	}
	if config.MaxHosts <= 0 {
		return fmt.Errorf("最大主机数必须大于0")
	}
	if config.IsTrial {
		if config.TrialDurationDays <= 0 {
			return fmt.Errorf("试用期天数必须大于0")
		}
	} else {
		if config.ValidYears <= 0 {
			return fmt.Errorf("有效年数必须大于0")
		}
	}
	if config.OutputFile == "" {
		return fmt.Errorf("输出文件名不能为空")
	}

	// 验证支持的操作系统
	validOS := map[string]bool{
		"windows": true,
		"linux":   true,
		"darwin":  true,
		"freebsd": true,
		"all":     true,
	}
	for _, os := range config.SupportedOS {
		if !validOS[strings.ToLower(os)] {
			return fmt.Errorf("不支持的操作系统类型: %s", os)
		}
	}

	return nil
}

// SaveLicenseConfig 保存授权配置到文件
func SaveLicenseConfig(config *LicenseConfig, filename string) error {
	// 验证配置
	if err := ValidateLicenseConfig(config); err != nil {
		return fmt.Errorf("配置验证失败: %v", err)
	}

	data, err := json.MarshalIndent(config, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化配置失败: %v", err)
	}

	err = os.WriteFile(filename, data, 0644)
	if err != nil {
		return fmt.Errorf("保存配置文件失败: %v", err)
	}

	fmt.Printf("配置文件已保存: %s\n", filename)
	return nil
}

// UpdateLicenseConfig 更新授权配置
func UpdateLicenseConfig(config *LicenseConfig, updates map[string]interface{}) error {
	for key, value := range updates {
		switch key {
		case "user_name":
			if v, ok := value.(string); ok {
				config.UserName = v
			}
		case "company":
			if v, ok := value.(string); ok {
				config.Company = v
			}
		case "product_name":
			if v, ok := value.(string); ok {
				config.ProductName = v
			}
		case "developer_name":
			if v, ok := value.(string); ok {
				config.DeveloperName = v
			}
		case "version":
			if v, ok := value.(string); ok {
				config.Version = v
			}
		case "is_trial":
			if v, ok := value.(bool); ok {
				config.IsTrial = v
			}
		case "valid_years":
			if v, ok := value.(float64); ok {
				config.ValidYears = int(v)
			}
		case "trial_duration_days":
			if v, ok := value.(float64); ok {
				config.TrialDurationDays = int(v)
			}
		case "max_hosts":
			if v, ok := value.(float64); ok {
				config.MaxHosts = int64(v)
			}
		case "output_file":
			if v, ok := value.(string); ok {
				config.OutputFile = v
			}
		case "register_host":
			if v, ok := value.(bool); ok {
				config.RegisterHost = v
			}
		case "supported_os":
			if v, ok := value.([]interface{}); ok {
				var osTypes []string
				for _, os := range v {
					if osStr, ok := os.(string); ok {
						osTypes = append(osTypes, osStr)
					}
				}
				config.SupportedOS = osTypes
			}
		default:
			return fmt.Errorf("未知的配置项: %s", key)
		}
	}

	return ValidateLicenseConfig(config)
}

// CompareLicenseConfigs 比较两个授权配置
func CompareLicenseConfigs(config1, config2 *LicenseConfig) map[string]interface{} {
	differences := make(map[string]interface{})

	if config1.UserName != config2.UserName {
		differences["user_name"] = map[string]string{"old": config1.UserName, "new": config2.UserName}
	}
	if config1.Company != config2.Company {
		differences["company"] = map[string]string{"old": config1.Company, "new": config2.Company}
	}
	if config1.ProductName != config2.ProductName {
		differences["product_name"] = map[string]string{"old": config1.ProductName, "new": config2.ProductName}
	}
	if config1.DeveloperName != config2.DeveloperName {
		differences["developer_name"] = map[string]string{"old": config1.DeveloperName, "new": config2.DeveloperName}
	}
	if config1.Version != config2.Version {
		differences["version"] = map[string]string{"old": config1.Version, "new": config2.Version}
	}
	if config1.IsTrial != config2.IsTrial {
		differences["is_trial"] = map[string]bool{"old": config1.IsTrial, "new": config2.IsTrial}
	}
	if config1.ValidYears != config2.ValidYears {
		differences["valid_years"] = map[string]int{"old": config1.ValidYears, "new": config2.ValidYears}
	}
	if config1.TrialDurationDays != config2.TrialDurationDays {
		differences["trial_duration_days"] = map[string]int{"old": config1.TrialDurationDays, "new": config2.TrialDurationDays}
	}
	if config1.MaxHosts != config2.MaxHosts {
		differences["max_hosts"] = map[string]int64{"old": config1.MaxHosts, "new": config2.MaxHosts}
	}
	if config1.OutputFile != config2.OutputFile {
		differences["output_file"] = map[string]string{"old": config1.OutputFile, "new": config2.OutputFile}
	}
	if config1.RegisterHost != config2.RegisterHost {
		differences["register_host"] = map[string]bool{"old": config1.RegisterHost, "new": config2.RegisterHost}
	}

	// 比较支持的操作系统
	if !equalStringSlices(config1.SupportedOS, config2.SupportedOS) {
		differences["supported_os"] = map[string][]string{"old": config1.SupportedOS, "new": config2.SupportedOS}
	}

	return differences
}

// equalStringSlices 比较两个字符串切片是否相等
func equalStringSlices(a, b []string) bool {
	if len(a) != len(b) {
		return false
	}
	for i, v := range a {
		if v != b[i] {
			return false
		}
	}
	return true
}

// ShowLicenseConfig 显示授权配置信息
func ShowLicenseConfig(config *LicenseConfig) {
	fmt.Println("=== 授权配置信息 ===")
	fmt.Printf("用户名: %s\n", config.UserName)
	fmt.Printf("公司: %s\n", config.Company)
	fmt.Printf("产品名: %s\n", config.ProductName)
	fmt.Printf("开发者: %s\n", config.DeveloperName)
	fmt.Printf("版本: %s\n", config.Version)
	fmt.Printf("是否试用版: %t\n", config.IsTrial)

	if config.IsTrial {
		fmt.Printf("试用期天数: %d\n", config.TrialDurationDays)
	} else {
		fmt.Printf("有效年数: %d\n", config.ValidYears)
	}

	fmt.Printf("最大主机数: %d\n", config.MaxHosts)
	fmt.Printf("输出文件: %s\n", config.OutputFile)
	fmt.Printf("注册当前主机: %t\n", config.RegisterHost)
	fmt.Printf("支持的操作系统: %v\n", config.SupportedOS)

	fmt.Println("\n客户端功能:")
	for i, feature := range config.Feature.ClientFeatures {
		fmt.Printf("  %d. %s - %s\n", i+1, feature.Name, feature.Desc)
	}

	fmt.Println("\n服务端功能:")
	for i, feature := range config.Feature.ServerFeatures {
		fmt.Printf("  %d. %s - %s\n", i+1, feature.Name, feature.Desc)
	}
}

// CreateLicenseConfigFromParams 从参数创建授权配置
func CreateLicenseConfigFromParams(userName, company, productName, version string,
	isTrial bool, validYears, trialDays int, maxHosts int64,
	featuresStr, osStr, outputFile string, registerHost bool) *LicenseConfig {

	// 解析功能列表
	features := parseFeatures(featuresStr)
	clientFeatures := make([]Feature, 0)
	serverFeatures := make([]Feature, 0)

	for _, feature := range features {
		clientFeatures = append(clientFeatures, Feature{Name: feature, Desc: feature + "功能", Allowed: true})
	}

	// 解析操作系统类型
	osTypes := parseOSTypes(osStr)
	supportedOS := make([]string, 0)
	for _, osType := range osTypes {
		supportedOS = append(supportedOS, string(osType))
	}

	config := &LicenseConfig{
		UserName:          userName,
		Company:           company,
		ProductName:       productName,
		DeveloperName:     "开发者",
		Version:           version,
		IsTrial:           isTrial,
		ValidYears:        validYears,
		TrialDurationDays: trialDays,
		MaxHosts:          maxHosts,
		Feature: Features{
			ClientFeatures: clientFeatures,
			ServerFeatures: serverFeatures,
		},
		SupportedOS:  supportedOS,
		OutputFile:   outputFile,
		RegisterHost: registerHost,
	}

	return config
}

// MergeLicenseConfigs 合并两个授权配置，第二个配置的值会覆盖第一个
func MergeLicenseConfigs(base, override *LicenseConfig) *LicenseConfig {
	merged := *base // 复制基础配置

	if override.UserName != "" {
		merged.UserName = override.UserName
	}
	if override.Company != "" {
		merged.Company = override.Company
	}
	if override.ProductName != "" {
		merged.ProductName = override.ProductName
	}
	if override.DeveloperName != "" {
		merged.DeveloperName = override.DeveloperName
	}
	if override.Version != "" {
		merged.Version = override.Version
	}
	if override.ValidYears > 0 {
		merged.ValidYears = override.ValidYears
	}
	if override.TrialDurationDays > 0 {
		merged.TrialDurationDays = override.TrialDurationDays
	}
	if override.MaxHosts > 0 {
		merged.MaxHosts = override.MaxHosts
	}
	if len(override.Feature.ClientFeatures) > 0 || len(override.Feature.ServerFeatures) > 0 {
		merged.Feature = override.Feature
	}
	if len(override.SupportedOS) > 0 {
		merged.SupportedOS = override.SupportedOS
	}
	if override.OutputFile != "" {
		merged.OutputFile = override.OutputFile
	}

	// 布尔值直接覆盖
	merged.IsTrial = override.IsTrial
	merged.RegisterHost = override.RegisterHost

	return &merged
}

// CloneLicenseConfig 克隆授权配置
func CloneLicenseConfig(config *LicenseConfig) *LicenseConfig {
	clone := *config

	// 深拷贝切片
	clone.SupportedOS = make([]string, len(config.SupportedOS))
	copy(clone.SupportedOS, config.SupportedOS)

	clone.Feature.ClientFeatures = make([]Feature, len(config.Feature.ClientFeatures))
	copy(clone.Feature.ClientFeatures, config.Feature.ClientFeatures)

	clone.Feature.ServerFeatures = make([]Feature, len(config.Feature.ServerFeatures))
	copy(clone.Feature.ServerFeatures, config.Feature.ServerFeatures)

	return &clone
}

// GetDefaultTrialConfig 获取默认试用版配置
func GetDefaultTrialConfig() *LicenseConfig {
	return &LicenseConfig{
		UserName:          "试用用户",
		Company:           "试用公司",
		ProductName:       "产品名称",
		DeveloperName:     "开发团队",
		Version:           "v1.0.0",
		IsTrial:           true,
		ValidYears:        0,
		TrialDurationDays: 30,
		MaxHosts:          1,
		Feature: Features{
			ClientFeatures: []Feature{
				{Name: "基础功能", Desc: "基本的客户端功能", Allowed: true},
			},
			ServerFeatures: []Feature{
				{Name: "基础服务", Desc: "基本的服务端功能", Allowed: true},
			},
		},
		SupportedOS:  []string{"all"},
		OutputFile:   "trial_license.json",
		RegisterHost: true,
	}
}

// GetDefaultFullConfig 获取默认正式版配置
func GetDefaultFullConfig() *LicenseConfig {
	return &LicenseConfig{
		UserName:          "正式用户",
		Company:           "正式公司",
		ProductName:       "产品名称",
		DeveloperName:     "开发团队",
		Version:           "v1.0.0",
		IsTrial:           false,
		ValidYears:        1,
		TrialDurationDays: 0,
		MaxHosts:          5,
		Feature: Features{
			ClientFeatures: []Feature{
				{Name: "基础功能", Desc: "基本的客户端功能", Allowed: true},
				{Name: "高级功能", Desc: "高级的客户端功能", Allowed: true},
				{Name: "专业功能", Desc: "专业的客户端功能", Allowed: true},
			},
			ServerFeatures: []Feature{
				{Name: "基础服务", Desc: "基本的服务端功能", Allowed: true},
				{Name: "高级服务", Desc: "高级的服务端功能", Allowed: true},
				{Name: "专业服务", Desc: "专业的服务端功能", Allowed: true},
			},
		},
		SupportedOS:  []string{"all"},
		OutputFile:   "full_license.json",
		RegisterHost: true,
	}
}

// GetEnterpriseConfig 获取企业版配置
func GetEnterpriseConfig() *LicenseConfig {
	return &LicenseConfig{
		UserName:          "企业用户",
		Company:           "企业公司",
		ProductName:       "企业产品",
		DeveloperName:     "开发团队",
		Version:           "v1.0.0",
		IsTrial:           false,
		ValidYears:        3,
		TrialDurationDays: 0,
		MaxHosts:          100,
		Feature: Features{
			ClientFeatures: []Feature{
				{Name: "基础功能", Desc: "基本的客户端功能", Allowed: true},
				{Name: "高级功能", Desc: "高级的客户端功能", Allowed: true},
				{Name: "专业功能", Desc: "专业的客户端功能", Allowed: true},
				{Name: "企业功能", Desc: "企业级的客户端功能", Allowed: true},
				{Name: "定制功能", Desc: "定制化的客户端功能", Allowed: true},
			},
			ServerFeatures: []Feature{
				{Name: "基础服务", Desc: "基本的服务端功能", Allowed: true},
				{Name: "高级服务", Desc: "高级的服务端功能", Allowed: true},
				{Name: "专业服务", Desc: "专业的服务端功能", Allowed: true},
				{Name: "企业服务", Desc: "企业级的服务端功能", Allowed: true},
				{Name: "集群服务", Desc: "集群化的服务端功能", Allowed: true},
			},
		},
		SupportedOS:  []string{"all"},
		OutputFile:   "enterprise_license.json",
		RegisterHost: false, // 企业版通常不自动注册主机
	}
}

// CreateConfigTemplate 创建配置模板
func CreateConfigTemplate(templateType string, filename string) error {
	var config *LicenseConfig

	switch strings.ToLower(templateType) {
	case "trial":
		config = GetDefaultTrialConfig()
	case "full":
		config = GetDefaultFullConfig()
	case "enterprise":
		config = GetEnterpriseConfig()
	default:
		return fmt.Errorf("未知的模板类型: %s，支持的类型: trial, full, enterprise", templateType)
	}

	return SaveLicenseConfig(config, filename)
}

// ExtractFeatureNames 从 Features 结构体中提取功能名称列表
func ExtractFeatureNames(features Features) []string {
	var featureNames []string

	for _, feature := range features.ClientFeatures {
		featureNames = append(featureNames, feature.Name)
	}

	for _, feature := range features.ServerFeatures {
		featureNames = append(featureNames, feature.Name)
	}

	if len(featureNames) == 0 {
		return []string{"基础功能"}
	}

	return featureNames
}

// CreateSampleConfig 创建示例配置文件
func CreateSampleConfig(filename string) error {
	config := LicenseConfig{
		UserName:          "张三",
		Company:           "ABC科技有限公司",
		ProductName:       "超级软件",
		DeveloperName:     "开发团队",
		Version:           "v1.0.0",
		IsTrial:           true,
		ValidYears:        0,
		TrialDurationDays: 30,
		MaxHosts:          2,
		Feature: Features{
			ClientFeatures: []Feature{
				{Name: "基础功能", Desc: "基本的客户端功能", Allowed: true},
				{Name: "高级功能", Desc: "高级的客户端功能", Allowed: true},
			},
			ServerFeatures: []Feature{
				{Name: "基础服务", Desc: "基本的服务端功能", Allowed: true},
				{Name: "高级服务", Desc: "高级的服务端功能", Allowed: true},
			},
		},
		SupportedOS:  []string{"windows", "linux", "darwin"},
		OutputFile:   "license.json",
		RegisterHost: true,
	}

	data, err := json.MarshalIndent(config, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化配置失败: %v", err)
	}

	err = os.WriteFile(filename, data, 0644)
	if err != nil {
		return fmt.Errorf("保存配置文件失败: %v", err)
	}

	fmt.Printf("示例配置文件已创建: %s\n", filename)
	return nil
}

// ShowHostInfo 显示当前主机信息
func ShowHostInfo() {
	fmt.Println("=== 当前主机信息 ===")

	currentHostInfo, err := GetCurrentHostInfo()
	if err != nil {
		fmt.Printf("获取主机信息失败: %v\n", err)
		return
	}

	fmt.Printf("主机名: %s\n", currentHostInfo.Hostname)
	fmt.Printf("操作系统: %s\n", currentHostInfo.OSType)
	fmt.Printf("系统架构: %s\n", currentHostInfo.OSVersion)
	fmt.Printf("主机指纹: %s\n", currentHostInfo.Fingerprint)
	fmt.Printf("当前时间: %s\n", time.Now().Format("2006-01-02 15:04:05"))
	nhf := sysinfo.AllInfoEx()
	fmt.Printf("\r\nbaseHostInfo:%#v\n", nhf)
}

// GenerateLicenseFromConfig 从配置文件生成授权文件
func GenerateLicenseFromConfig(manager *LicenseManager, config *LicenseConfig) {
	fmt.Println("=== 生成授权文件 ===")

	// 转换操作系统类型
	var osTypes []OSType
	hasAll := false
	for _, osStr := range config.SupportedOS {
		switch strings.ToLower(osStr) {
		case "windows":
			osTypes = append(osTypes, OSWindows)
		case "linux":
			osTypes = append(osTypes, OSLinux)
		case "darwin", "macos":
			osTypes = append(osTypes, OSDarwin)
		case "freebsd":
			osTypes = append(osTypes, OSFreeBSD)
		case "all":
			hasAll = true
		default:
			fmt.Printf("警告: 未知的操作系统类型 '%s'，已忽略\n", osStr)
		}
	}
	if hasAll || len(osTypes) == 0 {
		osTypes = []OSType{OSAll}
	}

	// 提取功能列表
	features := ExtractFeatureNames(config.Feature)

	// 创建授权信息
	var license LicenseInfo
	if config.IsTrial {
		license = CreateTrialLicense(config.UserName, config.Company, config.ProductName, config.Version, int(config.MaxHosts), features, osTypes, config.TrialDurationDays)
		fmt.Printf("创建试用授权 (有效期: %d 天)\n", config.TrialDurationDays)
	} else {
		license = CreateFullLicense(config.UserName, config.Company, config.ProductName, config.Version, int(config.MaxHosts), features, config.ValidYears, osTypes)
		fmt.Printf("创建正式授权 (有效期: %d 年)\n", config.ValidYears)
	}

	// 如果需要注册当前主机
	if config.RegisterHost {
		currentHostInfo, err := GetCurrentHostInfo()
		if err != nil {
			fmt.Printf("获取主机信息失败: %v\n", err)
			return
		}

		// 检查操作系统支持
		supported := false
		for _, supportedOS := range osTypes {
			if supportedOS == OSAll || supportedOS == currentHostInfo.OSType {
				supported = true
				break
			}
		}

		if !supported {
			fmt.Printf("警告: 当前操作系统 %s 不在授权支持范围内 %v\n", currentHostInfo.OSType, osTypes)
			fmt.Println("继续生成授权文件，但当前主机将无法使用...")
		} else {
			license.RegisteredHosts = []HostInfo{*currentHostInfo}
			fmt.Printf("已注册当前主机: %s (%s)\n", currentHostInfo.Hostname, currentHostInfo.OSType)
		}
	}

	// 生成签名授权
	signedLicense, err := manager.GenerateLicense(license)
	if err != nil {
		fmt.Printf("生成授权失败: %v\n", err)
		return
	}

	// 保存到文件
	err = manager.SaveLicense(signedLicense, config.OutputFile)
	if err != nil {
		fmt.Printf("保存授权文件失败: %v\n", err)
		return
	}

	// 显示授权信息
	status := manager.GetLicenseStatus(signedLicense)
	fmt.Printf("\n授权信息:\n")
	fmt.Printf("  用户: %s\n", status["user_name"])
	fmt.Printf("  公司: %s\n", status["company"])
	fmt.Printf("  产品: %s\n", status["product"])
	fmt.Printf("  类型: %s\n", status["license_type"])
	fmt.Printf("  支持的操作系统: %v\n", status["supported_os"])
	fmt.Printf("  最大主机数: %d\n", status["max_hosts"])
	fmt.Printf("  已注册主机数: %d\n", status["registered_hosts_count"])
	fmt.Printf("  功能: %v\n", status["features"])

	if config.IsTrial {
		fmt.Printf("  试用剩余天数: %d 天\n", status["trial_remaining_days"])
		fmt.Printf("  试用结束日期: %s\n", status["trial_end_date"])
	} else {
		fmt.Printf("  授权剩余天数: %d 天\n", status["remaining_days"])
		fmt.Printf("  授权到期日期: %s\n", status["expiry_date"])
	}

	fmt.Printf("\n授权文件已生成: %s\n", config.OutputFile)
}

// verifyLicense 验证授权文件
func VerifyLicense(manager *LicenseManager, filename string) error {
	fmt.Printf("=== 验证授权文件: %s ===\n", filename)

	// 加载授权文件
	signedLicense, err := manager.LoadLicense(filename)
	if err != nil {
		fmt.Printf("加载授权文件失败: %v\n", err)
		return err
	}

	// 获取当前主机信息
	currentHostInfo, err := GetCurrentHostInfo()
	if err != nil {
		fmt.Printf("获取主机信息失败: %v\n", err)
		return err
	}

	fmt.Printf("当前主机: %s (%s %s)\n", currentHostInfo.Hostname, currentHostInfo.OSType, currentHostInfo.OSVersion)
	fmt.Printf("主机指纹: %s\n\n", currentHostInfo.Fingerprint[:16]+"...")

	// 显示授权信息
	status := manager.GetLicenseStatus(signedLicense)
	fmt.Printf("授权信息:\n")
	fmt.Printf("  用户: %s\n", status["user_name"])
	fmt.Printf("  公司: %s\n", status["company"])
	fmt.Printf("  产品: %s\n", status["product"])
	fmt.Printf("  类型: %s\n", status["license_type"])
	fmt.Printf("  支持的操作系统: %v\n", status["supported_os"])
	fmt.Printf("  最大主机数: %d\n", status["max_hosts"])
	fmt.Printf("  已注册主机数: %d\n", status["registered_hosts_count"])
	fmt.Printf("  功能: %v\n", status["features"])

	if signedLicense.License.IsTrial {
		fmt.Printf("  试用剩余天数: %d 天\n", status["trial_remaining_days"])
		fmt.Printf("  试用结束日期: %s\n", status["trial_end_date"])
		if status["trial_expired"].(bool) {
			fmt.Printf("  ⚠️  试用期已过期\n")
		}
	} else {
		fmt.Printf("  授权剩余天数: %d 天\n", status["remaining_days"])
		fmt.Printf("  授权到期日期: %s\n", status["expiry_date"])
		if status["expired"].(bool) {
			fmt.Printf("  ⚠️  授权已过期\n")
		}
	}

	// 显示已注册主机
	if len(signedLicense.License.RegisteredHosts) > 0 {
		fmt.Printf("\n已注册主机:\n")
		for i, host := range signedLicense.License.RegisteredHosts {
			fmt.Printf("  %d. %s (%s %s) - 注册于 %s\n",
				i+1, host.Hostname, host.OSType, host.OSVersion,
				host.RegisterTime.Format("2006-01-02 15:04:05"))
			if host.Fingerprint == currentHostInfo.Fingerprint {
				fmt.Printf("     ✓ 当前主机\n")
			}
		}
	} else {
		fmt.Printf("\n尚未注册任何主机\n")
	}

	// 验证授权
	fmt.Printf("\n=== 授权验证结果 ===\n")

	// 基本授权验证
	if err := manager.VerifyLicense(signedLicense); err != nil {
		fmt.Printf("❌ 基本授权验证失败: %v\n", err)
		return err
	}
	fmt.Printf("✓ 基本授权验证通过\n")

	// 操作系统支持验证
	if err := manager.CheckOSSupport(signedLicense, currentHostInfo.OSType); err != nil {
		fmt.Printf("❌ 操作系统支持验证失败: %v\n", err)
		return err
	}
	fmt.Printf("✓ 操作系统支持验证通过\n")

	// 主机授权验证
	if err := manager.CheckHostLicense(signedLicense); err != nil {
		fmt.Printf("❌ 主机授权验证失败: %v\n", err)

		// 检查是否可以注册新主机
		if len(signedLicense.License.RegisteredHosts) < signedLicense.License.MaxHosts {
			fmt.Printf("\n💡 提示: 当前主机未注册，但授权还有空余位置 (%d/%d)\n",
				len(signedLicense.License.RegisteredHosts), signedLicense.License.MaxHosts)
			fmt.Printf("   可以使用以下命令注册当前主机:\n")
			fmt.Printf("   ./auth -mode=register -file=%s\n", filename)
		} else {
			fmt.Printf("\n❌ 授权已达到最大主机数量限制 (%d/%d)\n",
				len(signedLicense.License.RegisteredHosts), signedLicense.License.MaxHosts)
		}
		return err
	}

	fmt.Printf("✓ 主机授权验证通过\n")
	fmt.Printf("\n🎉 授权验证完全通过！当前主机可以正常使用该软件。\n")
	return nil
}
